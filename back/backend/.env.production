# DB_URL="mongodb://AdminCherry:HOM@********@localhost:27017/orbit"
# //For docker
# //DB_URL=*************************************************************************
# JWT_SECRET="HOM@********"

# issuer="<EMAIL>"
# audience="<EMAIL>"

# NODE_ENV="production"
# EDIT_MODE ="false"
# ignoreEnvFile="false"
# PORT=3000

# ControlPanelAdminPassword= "HOM@********"
# ControlPanelAdminPasswordViewer= "HOM@********"

# isOneSignalEnabled ="true"
# isFirebaseFcmEnabled ="false"

# oneSignalAppId="************************************"
# oneSignalApiKey="os_v2_app_hidtuivxmzhr3axkgux4tsfwn6ltqhi5umyeqc44flara6kba3pfxjlcr255auhtclw3363ub6gbvmiyif76qqwyluwdosxm7bgtdey"

# EMAIL_HOST="lim112.truehost.cloud"
# EMAIL_USER="<EMAIL>"
# EMAIL_PASSWORD="BulTWN(MN{pY"
# AGORA_APP_ID="********************************"
# AGORA_APP_CERTIFICATE="********************************"

# INLINE_RUNTIME_CHUNK=false

# # apnKeyId=YOUR KEY ID
# # apnAppBundle=YOUR APPLE BUNDLE
# # appleAccountTeamId=YOUR APPLE TEAM ID

DB_URL="mongodb://AdminCherry:HOM@********@localhost:27017/orbit"
# For docker
# DB_URL=*************************************************************************

JWT_SECRET="HOM@********"
issuer="<EMAIL>"
audience="<EMAIL>"

NODE_ENV="production"
EDIT_MODE="false"
ignoreEnvFile="false"
PORT=3000

ControlPanelAdminPassword="HOM@********"
ControlPanelAdminPasswordViewer="HOM@********"

isOneSignalEnabled="true"
isFirebaseFcmEnabled="false"

oneSignalAppId="************************************"
oneSignalApiKey="os_v2_app_hidtuivxmzhr3axkgux4tsfwn6ltqhi5umyeqc44flara6kba3pfxjlcr255auhtclw3363ub6gbvmiyif76qqwyluwdosxm7bgtdey"

EMAIL_HOST="lim112.truehost.cloud"
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="BulTWN(MN{pY"
AGORA_APP_ID="********************************"
AGORA_APP_CERTIFICATE="********************************"

INLINE_RUNTIME_CHUNK="false"

# apnKeyId=YOUR_KEY_ID
# apnAppBundle=YOUR_APPLE_BUNDLE
# appleAccountTeamId=YOUR_APPLE_TEAM_ID